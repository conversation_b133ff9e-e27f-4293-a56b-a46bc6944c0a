<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/QRCodeEmailService.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Enable error reporting for debugging
ini_set('display_errors', 0);
error_reporting(E_ALL);

header('Content-Type: application/json');

// Validate connection
if (!$conn) {
    error_log("RSVP Error: Database connection failed");
    echo json_encode(['success' => false, 'message' => 'Database connection error']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Log incoming request for debugging
error_log("RSVP Request: " . json_encode($_POST));

// Validate action
if (empty($_POST['action']) || $_POST['action'] !== 'rsvp') {
    echo json_encode(['success' => false, 'message' => 'Invalid action']);
    exit();
}

// Validate required fields
$required_fields = ['event_id', 'guest_name', 'guest_email'];
foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields']);
        exit();
    }
}

$event_id = (int)$_POST['event_id'];
$guest_name = trim($_POST['guest_name']);
$guest_email = trim($_POST['guest_email']);
$guest_phone = trim($_POST['guest_phone'] ?? '');
$party_size = (int)($_POST['party_size'] ?? 1);
$special_requirements = trim($_POST['special_requirements'] ?? '');

// Validate email
if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
    exit();
}

try {
    // First, ensure the event_rsvps table has the correct structure for guest RSVPs
    $conn->exec("
        CREATE TABLE IF NOT EXISTS event_rsvps_guests (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            guest_name VARCHAR(255) NOT NULL,
            guest_email VARCHAR(255) NOT NULL,
            guest_phone VARCHAR(20),
            status ENUM('attending', 'not_attending', 'maybe', 'waitlist') DEFAULT 'attending',
            party_size INT(11) DEFAULT 1,
            special_requirements TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_guest_email (guest_email),
            INDEX idx_status (status),
            UNIQUE KEY unique_event_guest (event_id, guest_email),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        )
    ");

    // Check if event exists and is active
    $stmt = $conn->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found or not active']);
        exit();
    }

    // Check if event is in the past
    if (strtotime($event['event_date']) < time()) {
        echo json_encode(['success' => false, 'message' => 'Cannot RSVP for past events']);
        exit();
    }

    // Check if user already has an RSVP for this event
    $stmt = $conn->prepare("SELECT id FROM event_rsvps_guests WHERE event_id = ? AND guest_email = ?");
    $stmt->execute([$event_id, $guest_email]);
    $existing_rsvp = $stmt->fetch();

    if ($existing_rsvp) {
        echo json_encode([
            'success' => false,
            'message' => "You have already RSVP'd for this event with the email address: {$guest_email}. Each email can only register once per event."
        ]);
        exit();
    }

    // Check capacity - count both member RSVPs and guest RSVPs
    $stmt = $conn->prepare("
        SELECT
            (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'attending') +
            (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending') as attending_count
    ");
    $stmt->execute([$event_id, $event_id]);
    $attending_count = $stmt->fetch(PDO::FETCH_ASSOC)['attending_count'];

    $status = 'attending';
    if ($event['max_attendees'] && $attending_count >= $event['max_attendees']) {
        $status = 'waitlist'; // Add to waitlist if event is full
    }

    // Insert RSVP into the guest table
    $stmt = $conn->prepare("
        INSERT INTO event_rsvps_guests (event_id, guest_name, guest_email, guest_phone, status, party_size, special_requirements, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");

    $stmt->execute([
        $event_id,
        $guest_name,
        $guest_email,
        $guest_phone,
        $status,
        $party_size,
        $special_requirements
    ]);

    // Generate and send QR code email for attending guests
    if ($status === 'attending') {
        $qrService = new QRCodeEmailService($conn);
        $qrEmailSent = $qrService->generateAndSendEventQRCode($event_id, null, $guest_email, $guest_name);

        if (!$qrEmailSent) {
            error_log("Failed to send QR code email to guest: $guest_email for event: $event_id");
        }
    }

    // Send basic confirmation email (optional)
    sendRSVPConfirmationEmail($guest_email, $guest_name, $event, $status);

    $message = $status === 'waitlist'
        ? 'You have been added to the waitlist for this event. We will notify you if a spot becomes available.'
        : 'Your RSVP has been confirmed! We look forward to seeing you at the event.';
    
    echo json_encode([
        'success' => true, 
        'message' => $message,
        'status' => $status
    ]);
    
} catch (PDOException $e) {
    error_log("RSVP PDO Error: " . $e->getMessage());
    error_log("RSVP PDO Error Code: " . $e->getCode());
    error_log("RSVP SQL State: " . $e->errorInfo[0] ?? 'Unknown');

    // Provide more specific error messages for common issues
    $error_message = 'An error occurred while processing your RSVP. Please try again.';

    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
        $error_message = 'You have already RSVP\'d for this event.';
    } elseif (strpos($e->getMessage(), 'foreign key constraint') !== false) {
        $error_message = 'Event not found. Please refresh the page and try again.';
    } elseif (strpos($e->getMessage(), "doesn't exist") !== false) {
        $error_message = 'Database table error. Please contact the administrator.';
    }

    echo json_encode(['success' => false, 'message' => $error_message, 'debug' => $e->getMessage()]);
} catch (Exception $e) {
    error_log("RSVP General Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An unexpected error occurred. Please try again.', 'debug' => $e->getMessage()]);
}

function sendRSVPConfirmationEmail($email, $name, $event, $status) {
    // This is a basic email function - you can enhance it based on your email system
    $subject = "RSVP Confirmation - " . $event['title'];
    
    $message = "Dear " . $name . ",\n\n";
    
    if ($status === 'waitlist') {
        $message .= "Thank you for your interest in " . $event['title'] . ". You have been added to the waitlist.\n\n";
        $message .= "We will notify you if a spot becomes available.\n\n";
    } else {
        $message .= "Thank you for your RSVP to " . $event['title'] . "!\n\n";
        $message .= "Event Details:\n";
        $message .= "Date: " . date('l, F j, Y \a\t g:i A', strtotime($event['event_date'])) . "\n";
        if ($event['location']) {
            $message .= "Location: " . $event['location'] . "\n";
        }
        $message .= "\nWe look forward to seeing you there!\n\n";
    }
    
    $message .= "Best regards,\n";
    $message .= get_organization_name();
    
    $headers = "From: " . get_organization_name() . " <noreply@" . $_SERVER['HTTP_HOST'] . ">\r\n";
    $headers .= "Reply-To: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Send email (you might want to use your existing email system)
    @mail($email, $subject, $message, $headers);
}
?>
