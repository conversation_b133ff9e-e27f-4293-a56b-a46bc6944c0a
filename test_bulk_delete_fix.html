<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Delete Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Bulk Delete Fix Test</h1>
        
        <div class="alert alert-info">
            <h4>Testing the Fix</h4>
            <p>This page tests if the modal-manager.js inclusion fixes the bulk delete functionality.</p>
            <p><strong>Expected behavior:</strong> No JavaScript errors in console when clicking bulk delete.</p>
        </div>

        <!-- Test Members Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>Test Members</h5>
                <!-- Bulk Actions -->
                <div id="bulkActionsContainer" style="display: none;">
                    <div class="btn-group shadow-sm" role="group">
                        <button type="button" class="btn btn-sm btn-danger" id="bulkDeleteBtn">
                            <i class="bi bi-trash me-1"></i>Delete Selected (<span id="selectedCount">0</span>)
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAllMembers" title="Select All">
                            </th>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input member-checkbox"
                                       value="1"
                                       data-name="Test User 1"
                                       data-email="<EMAIL>">
                            </td>
                            <td>1</td>
                            <td>Test User 1</td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input member-checkbox"
                                       value="2"
                                       data-name="Test User 2"
                                       data-email="<EMAIL>">
                            </td>
                            <td>2</td>
                            <td>Test User 2</td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input member-checkbox"
                                       value="3"
                                       data-name="Test User 3"
                                       data-email="<EMAIL>">
                            </td>
                            <td>3</td>
                            <td>Test User 3</td>
                            <td><EMAIL></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-4">
            <h4>Test Results</h4>
            <div id="testResults" class="alert alert-secondary">
                <p>Select some members and click "Delete Selected" to test the functionality.</p>
                <ul>
                    <li>✅ modal-manager.js should be loaded</li>
                    <li>✅ cleanupModalBackdrops function should be available</li>
                    <li>✅ No JavaScript errors should occur</li>
                    <li>✅ Bulk delete modal should open properly</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle me-2"></i>Confirm Bulk Member Deletion
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-shield-exclamation me-2"></i>⚠️ This action cannot be undone!</h6>
                        <p class="mb-0">You are about to permanently delete the selected members.</p>
                    </div>

                    <div class="mb-3">
                        <h6>Selected Members (<span id="bulkDeleteCount">0</span>):</h6>
                        <div id="bulkDeletePreview" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <!-- Member list will be populated here -->
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmBulkDelete">
                        <label class="form-check-label text-danger fw-bold" for="confirmBulkDelete">
                            I understand this is a permanent action
                        </label>
                    </div>

                    <div class="mb-3">
                        <label for="bulkDeleteConfirmText" class="form-label">
                            Type <strong>DELETE</strong> to confirm:
                        </label>
                        <input type="text" class="form-control" id="bulkDeleteConfirmText" placeholder="Type DELETE">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn" disabled>
                        <i class="bi bi-trash me-2"></i>Delete Selected Members
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="church/admin/js/modal-manager.js"></script>
    <script>
        // Test if modal-manager.js is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            
            // Check if cleanupModalBackdrops function exists
            if (typeof cleanupModalBackdrops === 'function') {
                testResults.innerHTML = '<div class="alert alert-success"><h5>✅ Fix Successful!</h5><p>modal-manager.js is loaded and cleanupModalBackdrops function is available.</p></div>';
            } else {
                testResults.innerHTML = '<div class="alert alert-danger"><h5>❌ Fix Failed!</h5><p>cleanupModalBackdrops function is not available. modal-manager.js may not be loaded.</p></div>';
            }
        });

        // Bulk Delete Functionality (simplified version from members.php)
        let selectedMembers = [];

        const selectAllCheckbox = document.getElementById('selectAllMembers');
        const memberCheckboxes = document.querySelectorAll('.member-checkbox');
        const bulkActionsContainer = document.getElementById('bulkActionsContainer');
        const selectedCountSpan = document.getElementById('selectedCount');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

        // Select All functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                memberCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateSelectedMembers();
            });
        }

        // Individual checkbox change
        memberCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedMembers);
        });

        // Update selected members array and UI
        function updateSelectedMembers() {
            selectedMembers = [];
            const checkedBoxes = document.querySelectorAll('.member-checkbox:checked');

            checkedBoxes.forEach(checkbox => {
                selectedMembers.push({
                    id: checkbox.value,
                    name: checkbox.dataset.name,
                    email: checkbox.dataset.email
                });
            });

            // Update UI
            if (selectedMembers.length > 0) {
                bulkActionsContainer.style.display = 'inline-block';
                selectedCountSpan.textContent = selectedMembers.length;
            } else {
                bulkActionsContainer.style.display = 'none';
            }
        }

        // Bulk delete button click
        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', function() {
                if (selectedMembers.length === 0) {
                    alert('Please select members to delete.');
                    return;
                }

                // Populate modal with selected members
                const bulkDeleteCount = document.getElementById('bulkDeleteCount');
                const bulkDeletePreview = document.getElementById('bulkDeletePreview');

                bulkDeleteCount.textContent = selectedMembers.length;

                let previewHtml = '<div class="row">';
                selectedMembers.forEach((member, index) => {
                    previewHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-fill text-danger me-2"></i>
                                <div>
                                    <strong>${member.name}</strong><br>
                                    <small class="text-muted">${member.email}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                previewHtml += '</div>';

                bulkDeletePreview.innerHTML = previewHtml;

                // Test cleanupModalBackdrops function
                try {
                    cleanupModalBackdrops();
                    console.log('✅ cleanupModalBackdrops() called successfully');
                } catch (error) {
                    console.error('❌ Error calling cleanupModalBackdrops():', error);
                }

                // Show modal
                const bulkDeleteModal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
                bulkDeleteModal.show();
            });
        }

        // Modal confirmation logic
        const confirmCheckbox = document.getElementById('confirmBulkDelete');
        const confirmTextInput = document.getElementById('bulkDeleteConfirmText');
        const confirmButton = document.getElementById('confirmBulkDeleteBtn');

        function updateConfirmButton() {
            const isChecked = confirmCheckbox && confirmCheckbox.checked;
            const isTextCorrect = confirmTextInput && confirmTextInput.value.toUpperCase() === 'DELETE';

            if (confirmButton) {
                confirmButton.disabled = !(isChecked && isTextCorrect);
            }
        }

        if (confirmCheckbox) {
            confirmCheckbox.addEventListener('change', updateConfirmButton);
        }

        if (confirmTextInput) {
            confirmTextInput.addEventListener('input', updateConfirmButton);
        }

        // Confirm bulk delete (test version - doesn't actually delete)
        if (confirmButton) {
            confirmButton.addEventListener('click', function() {
                alert('Test successful! In the real version, this would delete the selected members.');
                
                // Close modal and test cleanup
                const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                modal.hide();

                // Test cleanup function
                try {
                    cleanupModalBackdrops();
                    console.log('✅ Modal cleanup successful');
                } catch (error) {
                    console.error('❌ Modal cleanup failed:', error);
                }
            });
        }
    </script>
</body>
</html>
