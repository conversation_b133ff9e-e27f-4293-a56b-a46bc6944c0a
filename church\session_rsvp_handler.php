<?php
/**
 * Session RSVP Handler for Guests
 * Handles guest RSVPs for individual event sessions
 */

header('Content-Type: application/json');

try {
    // Include configuration
    require_once 'config.php';
    require_once 'includes/QRCodeEmailService.php';
    
    // Check if this is a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid request method'
        ]);
        exit();
    }
    
    // Get form data
    $action = $_POST['action'] ?? '';
    $event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
    $guest_name = trim($_POST['guest_name'] ?? '');
    $guest_email = trim($_POST['guest_email'] ?? '');
    $guest_phone = trim($_POST['guest_phone'] ?? '');
    $party_size = filter_input(INPUT_POST, 'party_size', FILTER_VALIDATE_INT) ?: 1;
    $special_requirements = trim($_POST['special_requirements'] ?? '');
    $selected_sessions = $_POST['selected_sessions'] ?? [];
    
    // Validate required fields
    if ($action !== 'session_rsvp') {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        exit();
    }
    
    if (!$event_id || empty($guest_name) || empty($guest_email)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please fill in all required fields'
        ]);
        exit();
    }
    
    if (empty($selected_sessions) || !is_array($selected_sessions)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please select at least one session'
        ]);
        exit();
    }
    
    // Validate email format
    if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please enter a valid email address'
        ]);
        exit();
    }
    
    // Verify event exists and is published
    $stmt = $pdo->prepare("
        SELECT id, title, event_date
        FROM events 
        WHERE id = ? AND (status = 'published' OR (status IS NULL AND is_active = 1))
    ");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo json_encode([
            'success' => false,
            'message' => 'Event not found or not available for registration'
        ]);
        exit();
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        $registered_sessions = [];
        $failed_sessions = [];
        
        foreach ($selected_sessions as $session_id) {
            $session_id = (int)$session_id;
            
            // Verify session exists and belongs to this event
            $stmt = $pdo->prepare("
                SELECT es.*, COUNT(sa.id) as current_registrations
                FROM event_sessions es
                LEFT JOIN session_attendance sa ON es.id = sa.session_id
                WHERE es.id = ? AND es.event_id = ? AND es.status = 'active'
                GROUP BY es.id
            ");
            $stmt->execute([$session_id, $event_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                $failed_sessions[] = "Session ID $session_id not found";
                continue;
            }
            
            // Check if session is in the past
            if (strtotime($session['start_datetime']) < time()) {
                $failed_sessions[] = "Session '{$session['session_title']}' has already started";
                continue;
            }
            
            // Check capacity
            if ($session['max_attendees'] && $session['current_registrations'] >= $session['max_attendees']) {
                $failed_sessions[] = "Session '{$session['session_title']}' is full";
                continue;
            }
            
            // Check if guest is already registered for this session
            $stmt = $pdo->prepare("
                SELECT id FROM session_attendance 
                WHERE session_id = ? AND guest_email = ?
            ");
            $stmt->execute([$session_id, $guest_email]);
            
            if ($stmt->fetch()) {
                $failed_sessions[] = "Already registered for session '{$session['session_title']}'";
                continue;
            }
            
            // Register guest for session
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance (
                    session_id, guest_name, guest_email, attendance_status, registration_date
                ) VALUES (?, ?, ?, 'registered', NOW())
            ");
            $stmt->execute([$session_id, $guest_name, $guest_email]);
            
            $registered_sessions[] = [
                'id' => $session_id,
                'title' => $session['session_title'],
                'start_datetime' => $session['start_datetime'],
                'end_datetime' => $session['end_datetime'],
                'location' => $session['location']
            ];
        }
        
        if (empty($registered_sessions)) {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'message' => 'No sessions could be registered: ' . implode(', ', $failed_sessions)
            ]);
            exit();
        }
        
        // Also create a main event RSVP record for the guest
        $stmt = $pdo->prepare("
            SELECT id FROM event_rsvps_guests 
            WHERE event_id = ? AND guest_email = ?
        ");
        $stmt->execute([$event_id, $guest_email]);
        
        if (!$stmt->fetch()) {
            // Create main event RSVP
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps_guests (
                    event_id, guest_name, guest_email, guest_phone, status, 
                    party_size, special_requirements, created_at
                ) VALUES (?, ?, ?, ?, 'attending', ?, ?, NOW())
            ");
            $stmt->execute([
                $event_id, $guest_name, $guest_email, $guest_phone, 
                $party_size, $special_requirements
            ]);
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Send confirmation emails for each registered session
        $qrService = new QRCodeEmailService($pdo);
        $email_results = [];
        
        foreach ($registered_sessions as $session) {
            try {
                $qrEmailSent = $qrService->generateAndSendSessionQRCode($session['id'], null, $guest_email, $guest_name);
                $email_results[] = $qrEmailSent ? 'sent' : 'failed';
            } catch (Exception $e) {
                error_log("Failed to send session QR code email: " . $e->getMessage());
                $email_results[] = 'failed';
            }
        }
        
        // Prepare success message
        $message = "Successfully registered for " . count($registered_sessions) . " session(s)";
        if (!empty($failed_sessions)) {
            $message .= ". Some sessions could not be registered: " . implode(', ', $failed_sessions);
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message,
            'registered_sessions' => $registered_sessions,
            'failed_sessions' => $failed_sessions,
            'emails_sent' => array_count_values($email_results)
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in session_rsvp_handler.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while processing your registration. Please try again.',
        'error' => $e->getMessage()
    ]);
}
?>
