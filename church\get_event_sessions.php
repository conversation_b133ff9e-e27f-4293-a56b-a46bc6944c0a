<?php
/**
 * Get Event Sessions API
 * Returns sessions for a specific event for public display
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include configuration
    require_once 'config.php';
    
    // Get event ID from request
    $event_id = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);
    
    if (!$event_id) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid event ID'
        ]);
        exit();
    }
    
    // Verify event exists and is published
    $stmt = $pdo->prepare("
        SELECT id, title 
        FROM events 
        WHERE id = ? AND (status = 'published' OR (status IS NULL AND is_active = 1))
    ");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo json_encode([
            'success' => false,
            'message' => 'Event not found or not published'
        ]);
        exit();
    }
    
    // Get sessions for this event
    $stmt = $pdo->prepare("
        SELECT es.*,
               COUNT(sa.id) as registered_count,
               COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY es.id
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format sessions for frontend
    $formatted_sessions = [];
    foreach ($sessions as $session) {
        $formatted_sessions[] = [
            'id' => (int)$session['id'],
            'session_title' => $session['session_title'],
            'session_description' => $session['session_description'],
            'start_datetime' => $session['start_datetime'],
            'end_datetime' => $session['end_datetime'],
            'location' => $session['location'],
            'instructor_name' => $session['instructor_name'],
            'max_attendees' => $session['max_attendees'] ? (int)$session['max_attendees'] : null,
            'registered_count' => (int)$session['registered_count'],
            'attended_count' => (int)$session['attended_count'],
            'status' => $session['status']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'sessions' => $formatted_sessions,
        'event' => [
            'id' => (int)$event['id'],
            'title' => $event['title']
        ],
        'total_count' => count($formatted_sessions)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_event_sessions.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load sessions',
        'error' => $e->getMessage()
    ]);
}
?>
