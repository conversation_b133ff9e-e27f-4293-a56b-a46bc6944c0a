# Session Enhancement for Public Events Page

## 🎯 **Enhancement Overview**

Added comprehensive session visibility and guest RSVP functionality to the public events page (`church/events.php`). Guests can now see event sessions and register for specific sessions directly from the public events listing.

## ✨ **New Features**

### 1. **Session Display in Event Cards**
- **Session Count Badge**: Shows number of available sessions (e.g., "3 Sessions")
- **Expandable Session List**: Click "View" to see all sessions for an event
- **Session Details**: Each session shows:
  - Session title and time range (e.g., "10:00 AM - 11:00 AM")
  - Location and instructor information
  - Current registration count and capacity
  - Availability status (Available/Full/Past)

### 2. **Guest Session RSVP**
- **Enhanced RSVP Modal**: New modal for events with sessions
- **Session Selection**: Guests can select multiple sessions to attend
- **Guest Information**: Collects name, email, phone, party size
- **Special Requirements**: Text area for dietary restrictions, accessibility needs, etc.
- **Validation**: Prevents registration for full or past sessions

### 3. **Backend Integration**
- **Session API**: `get_event_sessions.php` provides session data
- **<PERSON><PERSON> Handler**: `session_rsvp_handler.php` processes guest registrations
- **Database Integration**: Uses existing `session_attendance` table
- **Email Notifications**: Sends QR codes for each registered session

## 📁 **Files Modified/Created**

### Modified Files:
1. **`church/events.php`** - Main public events page
   - Added session count to event queries
   - Enhanced event cards with session display
   - Added session RSVP modal and JavaScript functionality
   - Added CSS for session styling

### New Files:
2. **`church/get_event_sessions.php`** - API endpoint for session data
3. **`church/session_rsvp_handler.php`** - Handles guest session registrations

## 🎨 **UI/UX Enhancements**

### Event Card Improvements:
- **Session Badge**: Shows session count prominently
- **Collapsible Sessions**: Expandable section to view session details
- **Smart RSVP Button**: Different behavior for events with/without sessions
- **Loading States**: Spinner while loading session data

### Session Display:
- **Professional Layout**: Card-based session items with clear hierarchy
- **Time Formatting**: User-friendly time ranges (12-hour format)
- **Status Indicators**: Color-coded badges for availability
- **Responsive Design**: Works on all device sizes

### RSVP Modal:
- **Organized Sections**: Guest info and session selection in separate cards
- **Multi-Selection**: Checkboxes for selecting multiple sessions
- **Real-time Validation**: Prevents invalid selections
- **Clear Instructions**: Helpful text and alerts

## 🔧 **Technical Implementation**

### Frontend (JavaScript):
```javascript
// Load sessions when expanding event card
function loadSessions(eventId) {
    fetch(`get_event_sessions.php?event_id=${eventId}`)
        .then(response => response.json())
        .then(data => displaySessions(container, data.sessions));
}

// Handle session RSVP with multiple session selection
function submitSessionRSVP() {
    const selectedSessions = [];
    document.querySelectorAll('input[name="selected_sessions[]"]:checked')
        .forEach(checkbox => selectedSessions.push(checkbox.value));
    // Submit to session_rsvp_handler.php
}
```

### Backend (PHP):
```php
// Get sessions with registration counts
$stmt = $pdo->prepare("
    SELECT es.*, COUNT(sa.id) as registered_count
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id ORDER BY es.start_datetime ASC
");

// Register guest for multiple sessions
foreach ($selected_sessions as $session_id) {
    $stmt = $pdo->prepare("
        INSERT INTO session_attendance (session_id, guest_name, guest_email, attendance_status)
        VALUES (?, ?, ?, 'registered')
    ");
}
```

## 🧪 **Testing Guide**

### Test Session Display:
1. **Create Test Event with Sessions**:
   - Go to admin panel: `church/admin/event_sessions.php?event_id=X`
   - Add 3-4 sessions with different times and capacities
   - Set some sessions to different statuses (active/full)

2. **Test Public Display**:
   - Visit `church/events.php`
   - Verify session count badge appears
   - Click "View" to expand sessions
   - Verify session details display correctly

### Test Guest RSVP:
1. **Single Session Event**:
   - Click RSVP on event without sessions
   - Should open standard RSVP modal

2. **Multi-Session Event**:
   - Click RSVP on event with sessions
   - Should open enhanced session RSVP modal
   - Select multiple sessions and submit
   - Verify confirmation emails are sent

3. **Edge Cases**:
   - Try registering for full sessions (should be disabled)
   - Try registering for past sessions (should be disabled)
   - Try submitting without selecting sessions (should show error)

### Test Data Integrity:
1. **Database Records**:
   - Check `session_attendance` table for guest registrations
   - Check `event_rsvps_guests` table for main event RSVP
   - Verify registration counts update correctly

2. **Email Notifications**:
   - Confirm QR code emails are sent for each session
   - Test email content and formatting
   - Verify QR codes work for check-in

## 🎯 **Benefits**

### For Guests:
- **Better Visibility**: Can see all available sessions before committing
- **Granular Control**: Register for specific sessions of interest
- **Informed Decisions**: See capacity and timing information upfront
- **Streamlined Process**: Single form for multiple session registrations

### For Organizers:
- **Better Planning**: More accurate attendance data per session
- **Reduced No-shows**: Guests commit to specific sessions
- **Improved Communication**: Targeted emails for each session
- **Enhanced Analytics**: Session-level registration data

### For System:
- **Consistent Experience**: Matches logged-in user functionality
- **Scalable Design**: Works with any number of sessions
- **Maintainable Code**: Clean separation of concerns
- **Future-Ready**: Foundation for additional session features

## 🚀 **Future Enhancements**

### Potential Additions:
1. **Session Waitlists**: Allow guests to join waitlists for full sessions
2. **Session Prerequisites**: Require certain sessions before others
3. **Session Categories**: Group related sessions together
4. **Session Ratings**: Allow feedback on completed sessions
5. **Session Reminders**: Send reminder emails before sessions start
6. **Mobile App Integration**: QR code scanning for mobile check-in

### Technical Improvements:
1. **Caching**: Cache session data for better performance
2. **Real-time Updates**: WebSocket updates for live capacity changes
3. **Advanced Validation**: Cross-session conflict detection
4. **Bulk Operations**: Admin tools for bulk session management
5. **Analytics Dashboard**: Session attendance analytics
6. **Integration APIs**: Connect with external calendar systems

This enhancement significantly improves the guest experience while providing organizers with better tools for managing multi-session events!
