<?php
require_once __DIR__ . '/config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Function to generate QR code for member registration
function generateMemberQRCode($event_id, $member_id, $guest_email) {
    global $pdo;

    try {
        // Get attendee details
        if ($member_id) {
            $stmt = $pdo->prepare("SELECT full_name, email FROM members WHERE id = ?");
            $stmt->execute([$member_id]);
            $attendee = $stmt->fetch(PDO::FETCH_ASSOC);
            $attendee_type = 'member';
        } else {
            $stmt = $pdo->prepare("SELECT guest_name as full_name, guest_email as email FROM event_rsvps_guests WHERE event_id = ? AND guest_email = ?");
            $stmt->execute([$event_id, $guest_email]);
            $attendee = $stmt->fetch(PDO::FETCH_ASSOC);
            $attendee_type = 'guest';
        }

        if (!$attendee) return false;

        // Check if QR code already exists
        $stmt = $pdo->prepare("
            SELECT id FROM member_qr_codes
            WHERE event_id = ? AND
            ((member_id = ? AND member_id IS NOT NULL) OR
             (guest_email = ? AND guest_email IS NOT NULL))
        ");
        $stmt->execute([$event_id, $member_id, $guest_email]);

        if ($stmt->fetch()) {
            return true; // QR code already exists
        }

        // Generate unique QR token
        $qr_token = 'QR_' . $event_id . '_' . bin2hex(random_bytes(16));

        // Insert QR code record
        $stmt = $pdo->prepare("
            INSERT INTO member_qr_codes
            (event_id, member_id, guest_email, qr_token, attendee_name, attendee_email, attendee_type)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_id,
            $member_id,
            $guest_email,
            $qr_token,
            $attendee['full_name'],
            $attendee['email'],
            $attendee_type
        ]);

        return true;

    } catch (Exception $e) {
        error_log("Error generating QR code: " . $e->getMessage());
        return false;
    }
}

// Ensure the guest RSVP table exists
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS event_rsvps_guests (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            guest_name VARCHAR(255) NOT NULL,
            guest_email VARCHAR(255) NOT NULL,
            guest_phone VARCHAR(20),
            status ENUM('attending', 'not_attending', 'maybe', 'waitlist') DEFAULT 'attending',
            party_size INT(11) DEFAULT 1,
            special_requirements TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_guest_email (guest_email),
            INDEX idx_status (status),
            UNIQUE KEY unique_event_guest (event_id, guest_email),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        )
    ");
} catch (PDOException $e) {
    error_log("Error creating event_rsvps_guests table: " . $e->getMessage());
}

// Get events
$search = $_GET['search'] ?? '';

// Only show published events (use new status system with fallback)
$activeCondition = "is_active = 1"; // Default fallback
try {
    // First try the new status column (preferred)
    $testStmt = $conn->query("SELECT status FROM events LIMIT 1");
    $activeCondition = "status = 'published'";
} catch (PDOException $e) {
    // Fallback to is_active column if status doesn't exist
    $activeCondition = "is_active = 1";
}

$where_conditions = [$activeCondition];
$params = [];

// Filter out past events for public display (only show current and future events)
$where_conditions[] = "event_date >= CURDATE()";

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ? OR location LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

try {
    $sql = "
        SELECT e.*,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
               ) as attending_count,
               (SELECT COUNT(*) FROM event_sessions es WHERE es.event_id = e.id AND es.status = 'active') as session_count,
               header_banner.file_path as header_banner_path,
               header_banner.file_type as header_banner_type,
               header_banner.file_name as header_banner_name,
               header_banner.alt_text as header_banner_alt
        FROM events e
        LEFT JOIN event_files header_banner ON e.id = header_banner.event_id
            AND header_banner.is_header_banner = 1
        $where_clause
        ORDER BY e.event_date ASC
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Fallback query without header banner fields if they don't exist
    $sql = "
        SELECT e.*,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
               ) as attending_count,
               (SELECT COUNT(*) FROM event_sessions es WHERE es.event_id = e.id AND es.status = 'active') as session_count
        FROM events e
        $where_clause
        ORDER BY e.event_date ASC
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add empty header banner fields for compatibility
    foreach ($events as &$event) {
        $event['header_banner_path'] = null;
        $event['header_banner_type'] = null;
        $event['header_banner_name'] = null;
        $event['header_banner_alt'] = null;
    }
}

// Function to get sessions for an event
function getEventSessions($conn, $event_id) {
    try {
        $stmt = $conn->prepare("
            SELECT es.*,
                   COUNT(sa.id) as registered_count,
                   COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
            FROM event_sessions es
            LEFT JOIN session_attendance sa ON es.id = sa.session_id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY es.id
            ORDER BY es.start_datetime ASC
        ");
        $stmt->execute([$event_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading sessions for event $event_id: " . $e->getMessage());
        return [];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upcoming Events - <?php echo htmlspecialchars(get_organization_name()); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/promotional-materials.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/frontend_css.php'; ?>

    <style>
        .event-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: var(--bs-border-radius, 0.375rem);
        }
        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .event-date {
            background: linear-gradient(135deg, var(--bs-primary, #6f42c1), var(--bs-secondary, #5a32a3));
            color: white;
            border-radius: var(--bs-border-radius, 0.375rem);
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
        }
        .event-date .day {
            font-size: 2rem;
            font-weight: bold;
            line-height: 1;
        }
        .event-date .month {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .event-date .time {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .hero-section {
            background: linear-gradient(135deg, var(--bs-primary, #6f42c1), var(--bs-secondary, #5a32a3));
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .capacity-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 5px 10px;
            border-radius: var(--bs-border-radius, 0.375rem);
            font-size: 0.8rem;
        }
        .event-location {
            color: var(--bs-secondary, #6c757d);
            font-size: 0.9rem;
        }
        .btn-primary {
            background-color: var(--bs-primary, #007bff);
            border-color: var(--bs-primary, #007bff);
        }
        .btn-success {
            background-color: var(--bs-success, #28a745);
            border-color: var(--bs-success, #28a745);
        }
        .search-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: var(--bs-border-radius, 0.375rem);
            padding: 20px;
        }
        .sessions-section {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        .session-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 3px solid var(--bs-primary, #007bff);
        }
        .session-item:last-child {
            margin-bottom: 0;
        }
        .session-time {
            font-weight: 600;
            color: var(--bs-primary, #007bff);
        }
        .session-location {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .session-capacity {
            font-size: 0.8rem;
        }
        .session-rsvp-btn {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .session-modal-card {
            border: 1px solid #dee2e6;
            border-radius: 12px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .session-modal-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .session-modal-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .session-modal-body {
            padding: 20px;
        }
        .session-time-badge {
            background: var(--bs-primary, #007bff);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        .session-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        .session-meta-item {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .session-meta-item i {
            margin-right: 8px;
            width: 16px;
        }
        .session-status-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .session-description {
            color: #495057;
            line-height: 1.6;
            margin-top: 15px;
        }
        .session-rsvp-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'navbar.php'; ?>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1 class="display-4 mb-3">Upcoming Events</h1>
                    <p class="lead">Join us for these exciting events and activities at <?php echo get_organization_name(); ?></p>
                </div>
                <div class="col-md-4">
                    <form method="GET" class="d-flex">
                        <input type="text" class="form-control me-2" name="search" 
                               placeholder="Search events..." value="<?= htmlspecialchars($search) ?>">
                        <button type="submit" class="btn btn-light">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Section -->
    <div class="container">
        <?php if (empty($events)): ?>
            <div class="row">
                <div class="col-md-12 text-center">
                    <div class="py-5">
                        <i class="bi bi-calendar-x display-1 text-muted"></i>
                        <h3 class="mt-3">No Events Found</h3>
                        <p class="text-muted">
                            <?php if (!empty($search)): ?>
                                No events match your search criteria. <a href="events.php">View all events</a>
                            <?php else: ?>
                                There are no upcoming events at this time. Please check back later.
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <?php
            // Generate URL-friendly slug function
            function generateEventSlug($title) {
                $slug = strtolower($title);
                $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
                $slug = preg_replace('/[\s-]+/', '-', $slug);
                return trim($slug, '-');
            }
            ?>
            <div class="row">
                <?php foreach ($events as $event): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card event-card h-100">
                            <!-- Header Banner -->
                            <?php if (!empty($event['header_banner_path'])): ?>
                                <div class="card-img-top-container" style="height: 200px; overflow: hidden; position: relative;">
                                    <?php if (!empty($event['header_banner_type']) && strpos($event['header_banner_type'], 'image/') === 0): ?>
                                        <?php
                                        // Fix file path for public access
                                        $banner_path = fix_file_path_for_public($event['header_banner_path']);
                                        ?>
                                        <img src="<?= htmlspecialchars($banner_path) ?>"
                                             alt="<?= htmlspecialchars(($event['header_banner_alt'] ?? '') ?: $event['title']) ?>"
                                             class="card-img-top"
                                             style="width: 100%; height: 100%; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                                            <div class="text-center">
                                                <i class="bi bi-file-pdf fs-1 text-danger mb-2"></i>
                                                <div class="small text-muted">PDF Banner</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Overlay for better text readability -->
                                    <div class="position-absolute top-0 start-0 w-100 h-100"
                                         style="background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));"></div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body position-relative">
                                <!-- Capacity Badge -->
                                <?php if ($event['max_attendees']): ?>
                                    <div class="capacity-badge">
                                        <i class="bi bi-people"></i> 
                                        <?= $event['attending_count'] ?>/<?= $event['max_attendees'] ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Event Date -->
                                <div class="event-date">
                                    <div class="day"><?= date('j', strtotime($event['event_date'])) ?></div>
                                    <div class="month"><?= date('M Y', strtotime($event['event_date'])) ?></div>
                                    <div class="time"><?= date('g:i A', strtotime($event['event_date'])) ?></div>
                                </div>
                                
                                <!-- Event Details -->
                                <h5 class="card-title"><?= htmlspecialchars($event['title']) ?></h5>
                                
                                <?php if ($event['location']): ?>
                                    <p class="event-location">
                                        <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($event['location']) ?>
                                    </p>
                                <?php endif; ?>
                                
                                <p class="card-text">
                                    <?= htmlspecialchars(substr($event['description'], 0, 120)) ?>
                                    <?php if (strlen($event['description']) > 120): ?>...<?php endif; ?>
                                </p>

                                <!-- Sessions Section -->
                                <?php if ($event['session_count'] > 0): ?>
                                    <div class="sessions-section mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-collection text-primary me-2"></i>
                                                <span class="fw-medium"><?= $event['session_count'] ?> Session<?= $event['session_count'] > 1 ? 's' : '' ?> Available</span>
                                            </div>
                                            <button class="btn btn-outline-primary btn-sm view-sessions-btn" type="button"
                                                    data-event-id="<?= $event['id'] ?>"
                                                    data-event-title="<?= htmlspecialchars($event['title']) ?>">
                                                <i class="bi bi-eye me-1"></i> View Sessions
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Action Buttons -->
                                <div class="mt-auto">
                                    <?php
                                    $event_slug = generateEventSlug($event['title']);
                                    ?>
                                    <a href="event_detail.php?id=<?= $event_slug ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-info-circle"></i> View Details
                                    </a>

                                    <?php
                                    $is_full = $event['max_attendees'] && $event['attending_count'] >= $event['max_attendees'];
                                    $is_past = strtotime($event['event_date']) < time();
                                    ?>

                                    <?php if (!$is_past && !$is_full): ?>
                                        <button class="btn btn-success btn-sm" onclick="rsvpEvent(<?= $event['id'] ?>)">
                                            <i class="bi bi-check-circle"></i> RSVP
                                        </button>
                                    <?php elseif ($is_full): ?>
                                        <span class="badge bg-warning">Event Full</span>
                                    <?php elseif ($is_past): ?>
                                        <span class="badge bg-secondary">Past Event</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <p class="text-muted mb-0">&copy; <?= date('Y') ?> <?php echo get_organization_name(); ?>. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- RSVP Modal -->
    <div class="modal fade" id="rsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">RSVP for Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rsvpForm">
                        <input type="hidden" id="rsvp_event_id" name="event_id">
                        
                        <div class="mb-3">
                            <label for="guest_name" class="form-label">Your Name *</label>
                            <input type="text" class="form-control" id="guest_name" name="guest_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="guest_phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="guest_phone" name="guest_phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="party_size" class="form-label">Party Size</label>
                            <select class="form-select" id="party_size" name="party_size">
                                <option value="1">1 Person</option>
                                <option value="2">2 People</option>
                                <option value="3">3 People</option>
                                <option value="4">4 People</option>
                                <option value="5">5+ People</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessions Viewing Modal -->
    <div class="modal fade" id="sessionsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sessionsModalTitle">Event Sessions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="sessionsModalContent">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading sessions...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading sessions...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Session RSVP Modal -->
    <div class="modal fade" id="sessionRsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sessionRsvpModalTitle">RSVP for Session</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="sessionRsvpForm">
                        <input type="hidden" id="session_rsvp_session_id" name="session_id">
                        <input type="hidden" id="session_rsvp_event_id" name="event_id">

                        <!-- Session Info Display -->
                        <div class="alert alert-info" id="sessionInfoDisplay">
                            <!-- Session details will be shown here -->
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="session_guest_name" class="form-label">Your Name *</label>
                                    <input type="text" class="form-control" id="session_guest_name" name="guest_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="session_guest_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="session_guest_email" name="guest_email" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="session_guest_phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="session_guest_phone" name="guest_phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="session_party_size" class="form-label">Party Size</label>
                                    <select class="form-select" id="session_party_size" name="party_size">
                                        <option value="1">1 Person</option>
                                        <option value="2">2 People</option>
                                        <option value="3">3 People</option>
                                        <option value="4">4 People</option>
                                        <option value="5">5+ People</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="session_special_requirements" class="form-label">Special Requirements</label>
                            <textarea class="form-control" id="session_special_requirements" name="special_requirements" rows="3"
                                      placeholder="Any dietary restrictions, accessibility needs, or other special requirements..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="submitIndividualSessionRSVP()">Submit RSVP</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function rsvpEvent(eventId) {
            document.getElementById('rsvp_event_id').value = eventId;
            new bootstrap.Modal(document.getElementById('rsvpModal')).show();
        }

        function viewEventSessions(eventId, eventTitle) {
            // Set modal title
            document.getElementById('sessionsModalTitle').textContent = `${eventTitle} - Sessions`;

            // Show loading state
            document.getElementById('sessionsModalContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading sessions...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading sessions...</p>
                </div>
            `;

            // Show modal
            new bootstrap.Modal(document.getElementById('sessionsModal')).show();

            // Load sessions
            fetch(`get_event_sessions.php?event_id=${eventId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displaySessionsInModal(data.sessions, eventId);
                    } else {
                        document.getElementById('sessionsModalContent').innerHTML =
                            '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> Unable to load sessions.</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading sessions:', error);
                    document.getElementById('sessionsModalContent').innerHTML =
                        '<div class="alert alert-danger"><i class="bi bi-x-circle"></i> Error loading sessions. Please try again.</div>';
                });
        }

        function displaySessionsInModal(sessions, eventId) {
            const container = document.getElementById('sessionsModalContent');

            if (sessions.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-x display-4 text-muted"></i>
                        <h5 class="mt-3">No Sessions Available</h5>
                        <p class="text-muted">There are no sessions scheduled for this event.</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row">';

            sessions.forEach(session => {
                const startTime = new Date(session.start_datetime);
                const endTime = new Date(session.end_datetime);
                const timeRange = formatTimeRange(startTime, endTime);
                const is_full = session.max_attendees && session.registered_count >= session.max_attendees;
                const is_past = startTime < new Date();
                const can_register = !is_past && !is_full;

                let statusBadge = '';
                let statusClass = '';
                if (is_past) {
                    statusBadge = '<span class="session-status-badge bg-secondary">Past</span>';
                    statusClass = 'opacity-75';
                } else if (is_full) {
                    statusBadge = '<span class="session-status-badge bg-warning text-dark">Full</span>';
                    statusClass = 'opacity-75';
                } else {
                    statusBadge = '<span class="session-status-badge bg-success">Available</span>';
                }

                html += `
                    <div class="col-lg-6 mb-4">
                        <div class="session-modal-card ${statusClass}">
                            <div class="session-modal-header position-relative">
                                ${statusBadge}
                                <h6 class="mb-2 fw-bold">${session.session_title}</h6>
                                <div class="session-time-badge">${timeRange}</div>
                            </div>
                            <div class="session-modal-body">
                                <div class="session-meta">
                                    ${session.location ? `
                                        <div class="session-meta-item">
                                            <i class="bi bi-geo-alt"></i>
                                            <span>${session.location}</span>
                                        </div>
                                    ` : ''}
                                    ${session.instructor_name ? `
                                        <div class="session-meta-item">
                                            <i class="bi bi-person"></i>
                                            <span>${session.instructor_name}</span>
                                        </div>
                                    ` : ''}
                                    <div class="session-meta-item">
                                        <i class="bi bi-people"></i>
                                        <span>${session.registered_count}${session.max_attendees ? `/${session.max_attendees}` : ''} registered</span>
                                    </div>
                                </div>

                                ${session.session_description ? `
                                    <div class="session-description">
                                        ${session.session_description}
                                    </div>
                                ` : ''}

                                <div class="session-rsvp-section">
                                    ${can_register ? `
                                        <button class="btn btn-primary btn-sm" onclick="rsvpForSession(${session.id}, ${eventId}, '${session.session_title.replace(/'/g, "\\'")}')">
                                            <i class="bi bi-check-circle me-1"></i> RSVP for this Session
                                        </button>
                                    ` : `
                                        <button class="btn btn-secondary btn-sm" disabled>
                                            <i class="bi bi-x-circle me-1"></i> ${is_past ? 'Session Ended' : 'Session Full'}
                                        </button>
                                    `}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        function rsvpForSession(sessionId, eventId, sessionTitle) {
            // Set form values
            document.getElementById('session_rsvp_session_id').value = sessionId;
            document.getElementById('session_rsvp_event_id').value = eventId;
            document.getElementById('sessionRsvpModalTitle').textContent = `RSVP for ${sessionTitle}`;

            // Load session details for display
            fetch(`get_event_sessions.php?event_id=${eventId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const session = data.sessions.find(s => s.id == sessionId);
                        if (session) {
                            const startTime = new Date(session.start_datetime);
                            const endTime = new Date(session.end_datetime);
                            const timeRange = formatTimeRange(startTime, endTime);

                            document.getElementById('sessionInfoDisplay').innerHTML = `
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-calendar-event me-2"></i>
                                    <div>
                                        <strong>${session.session_title}</strong><br>
                                        <small class="text-muted">
                                            ${timeRange}
                                            ${session.location ? ` • ${session.location}` : ''}
                                            ${session.instructor_name ? ` • ${session.instructor_name}` : ''}
                                        </small>
                                    </div>
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading session details:', error);
                });

            // Show modal
            new bootstrap.Modal(document.getElementById('sessionRsvpModal')).show();
        }

        function formatTimeRange(startTime, endTime) {
            const options = {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            };
            const start = startTime.toLocaleTimeString('en-US', options);
            const end = endTime.toLocaleTimeString('en-US', options);
            return `${start} - ${end}`;
        }
        
        function submitRSVP() {
            const form = document.getElementById('rsvpForm');
            const formData = new FormData(form);
            formData.append('action', 'rsvp');

            // Log form data for debugging
            console.log('Submitting RSVP with data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }

            // Disable submit button while processing
            const submitButton = document.querySelector('.modal-footer .btn-primary');
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';

            fetch('rsvp_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-3';
                    successAlert.textContent = data.message || 'RSVP submitted successfully!';
                    form.insertAdjacentElement('beforebegin', successAlert);
                    
                    // Close modal after delay
                    setTimeout(() => {
                        bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                        location.reload();
                    }, 2000);
                } else {
                    throw new Error(data.message || 'RSVP submission failed');
                }
            })
            .catch(error => {
                console.error('RSVP Error:', error);

                // Show error message in modal
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-3';
                errorAlert.innerHTML = '<strong>Error:</strong> ' + (error.message || 'An error occurred. Please try again.');

                // Remove any existing alerts first
                const existingAlerts = form.parentNode.querySelectorAll('.alert');
                existingAlerts.forEach(alert => alert.remove());

                form.insertAdjacentElement('beforebegin', errorAlert);

                // Remove error message after 8 seconds
                setTimeout(() => {
                    if (errorAlert.parentNode) {
                        errorAlert.remove();
                    }
                }, 8000);
            })
            .finally(() => {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Submit RSVP';
            });
        }

        function submitIndividualSessionRSVP() {
            const form = document.getElementById('sessionRsvpForm');
            const formData = new FormData(form);
            formData.append('action', 'individual_session_rsvp');

            // Log form data for debugging
            console.log('Submitting Individual Session RSVP with data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }

            // Disable submit button while processing
            const submitButton = document.querySelector('#sessionRsvpModal .modal-footer .btn-success');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';

            fetch('individual_session_rsvp_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-3';
                    successAlert.innerHTML = '<i class="bi bi-check-circle"></i> ' + (data.message || 'RSVP submitted successfully! You will receive a confirmation email.');
                    form.insertAdjacentElement('beforebegin', successAlert);

                    // Reset form
                    form.reset();

                    // Close modal after delay and refresh sessions
                    setTimeout(() => {
                        bootstrap.Modal.getInstance(document.getElementById('sessionRsvpModal')).hide();

                        // Refresh the sessions modal if it's open
                        const sessionsModal = document.getElementById('sessionsModal');
                        if (sessionsModal.classList.contains('show')) {
                            const eventId = document.getElementById('session_rsvp_event_id').value;
                            const eventTitle = document.getElementById('sessionsModalTitle').textContent.split(' - ')[0];
                            viewEventSessions(eventId, eventTitle);
                        }
                    }, 2000);
                } else {
                    throw new Error(data.message || 'RSVP submission failed');
                }
            })
            .catch(error => {
                console.error('Session RSVP Error:', error);

                // Show error message in modal
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-3';
                errorAlert.innerHTML = '<i class="bi bi-x-circle"></i> <strong>Error:</strong> ' + (error.message || 'An error occurred. Please try again.');

                // Remove any existing alerts first
                const existingAlerts = form.parentNode.querySelectorAll('.alert');
                existingAlerts.forEach(alert => alert.remove());

                form.insertAdjacentElement('beforebegin', errorAlert);

                // Remove error message after 8 seconds
                setTimeout(() => {
                    if (errorAlert.parentNode) {
                        errorAlert.remove();
                    }
                }, 8000);
            })
            .finally(() => {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
        }

        // Event delegation for View Sessions buttons
        document.addEventListener('click', function(e) {
            if (e.target.closest('.view-sessions-btn')) {
                const button = e.target.closest('.view-sessions-btn');
                const eventId = button.getAttribute('data-event-id');
                const eventTitle = button.getAttribute('data-event-title');
                viewEventSessions(eventId, eventTitle);
            }
        });
    </script>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo htmlspecialchars(get_organization_name()); ?></h5>
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="../index.html" class="text-light me-3">Home</a>
                    <a href="events.php" class="text-light me-3">Events</a>
                    <a href="enhanced_donate.php" class="text-light">Donate</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
