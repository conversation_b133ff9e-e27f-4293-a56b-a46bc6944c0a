<?php
/**
 * Individual Session RSVP Handler for Guests
 * Handles guest RSVPs for a single event session
 */

header('Content-Type: application/json');

try {
    // Include configuration
    require_once 'config.php';
    require_once 'includes/QRCodeEmailService.php';
    
    // Check if this is a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid request method'
        ]);
        exit();
    }
    
    // Get form data
    $action = $_POST['action'] ?? '';
    $session_id = filter_input(INPUT_POST, 'session_id', FILTER_VALIDATE_INT);
    $event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
    $guest_name = trim($_POST['guest_name'] ?? '');
    $guest_email = trim($_POST['guest_email'] ?? '');
    $guest_phone = trim($_POST['guest_phone'] ?? '');
    $party_size = filter_input(INPUT_POST, 'party_size', FILTER_VALIDATE_INT) ?: 1;
    $special_requirements = trim($_POST['special_requirements'] ?? '');
    
    // Validate required fields
    if ($action !== 'individual_session_rsvp') {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        exit();
    }
    
    if (!$session_id || !$event_id || empty($guest_name) || empty($guest_email)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please fill in all required fields'
        ]);
        exit();
    }
    
    // Validate email format
    if (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please enter a valid email address'
        ]);
        exit();
    }
    
    // Verify session exists and belongs to the event
    $stmt = $pdo->prepare("
        SELECT es.*, e.title as event_title, e.event_date,
               COUNT(sa.id) as current_registrations
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.id = ? AND es.event_id = ? AND es.status = 'active'
          AND (e.status = 'published' OR (e.status IS NULL AND e.is_active = 1))
        GROUP BY es.id
    ");
    $stmt->execute([$session_id, $event_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo json_encode([
            'success' => false,
            'message' => 'Session not found or not available for registration'
        ]);
        exit();
    }
    
    // Check if session is in the past
    if (strtotime($session['start_datetime']) < time()) {
        echo json_encode([
            'success' => false,
            'message' => 'Cannot register for past sessions. This session has already started or ended.'
        ]);
        exit();
    }
    
    // Check capacity
    if ($session['max_attendees'] && $session['current_registrations'] >= $session['max_attendees']) {
        echo json_encode([
            'success' => false,
            'message' => 'This session is full. Please try another session.'
        ]);
        exit();
    }
    
    // Check if guest is already registered for this session
    $stmt = $pdo->prepare("
        SELECT id FROM session_attendance 
        WHERE session_id = ? AND guest_email = ?
    ");
    $stmt->execute([$session_id, $guest_email]);
    
    if ($stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => 'You are already registered for this session.'
        ]);
        exit();
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Register guest for session
        $stmt = $pdo->prepare("
            INSERT INTO session_attendance (
                session_id, guest_name, guest_email, attendance_status, registration_date
            ) VALUES (?, ?, ?, 'registered', NOW())
        ");
        $stmt->execute([$session_id, $guest_name, $guest_email]);
        
        // Also create or update main event RSVP record for the guest
        $stmt = $pdo->prepare("
            SELECT id FROM event_rsvps_guests 
            WHERE event_id = ? AND guest_email = ?
        ");
        $stmt->execute([$event_id, $guest_email]);
        
        if (!$stmt->fetch()) {
            // Create main event RSVP
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps_guests (
                    event_id, guest_name, guest_email, guest_phone, status, 
                    party_size, special_requirements, created_at
                ) VALUES (?, ?, ?, ?, 'attending', ?, ?, NOW())
            ");
            $stmt->execute([
                $event_id, $guest_name, $guest_email, $guest_phone, 
                $party_size, $special_requirements
            ]);
        } else {
            // Update existing RSVP if needed
            $stmt = $pdo->prepare("
                UPDATE event_rsvps_guests 
                SET guest_name = ?, guest_phone = ?, party_size = ?, 
                    special_requirements = ?, status = 'attending', updated_at = NOW()
                WHERE event_id = ? AND guest_email = ?
            ");
            $stmt->execute([
                $guest_name, $guest_phone, $party_size, 
                $special_requirements, $event_id, $guest_email
            ]);
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Send confirmation email with QR code
        $qrService = new QRCodeEmailService($pdo);
        $qrEmailSent = false;
        
        try {
            $qrEmailSent = $qrService->generateAndSendSessionQRCode($session_id, null, $guest_email, $guest_name);
        } catch (Exception $e) {
            error_log("Failed to send session QR code email: " . $e->getMessage());
        }
        
        // Format session time for response
        $startTime = new DateTime($session['start_datetime']);
        $endTime = new DateTime($session['end_datetime']);
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully registered for '{$session['session_title']}'. " . 
                        ($qrEmailSent ? "A confirmation email with QR code has been sent to {$guest_email}." : 
                         "Registration confirmed, but there was an issue sending the confirmation email."),
            'session' => [
                'id' => (int)$session['id'],
                'title' => $session['session_title'],
                'start_datetime' => $session['start_datetime'],
                'end_datetime' => $session['end_datetime'],
                'location' => $session['location']
            ],
            'email_sent' => $qrEmailSent
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in individual_session_rsvp_handler.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while processing your registration. Please try again.',
        'error' => $e->getMessage()
    ]);
}
?>
